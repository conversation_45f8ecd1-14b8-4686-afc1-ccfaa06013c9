class AddressAutocomplete {
    constructor() {
        this.Place = null;
        this.AutocompleteSessionToken = null;
        this.AutocompleteSuggestion = null;
        this.sessionToken = null;
        this.currentSuggestions = [];
        this.selectedIndex = -1;

        this.addressInput = document.getElementById('address-input');
        this.suggestionsDropdown = document.getElementById('suggestions-dropdown');
        this.selectedAddressDiv = document.getElementById('selected-address');
        this.form = document.getElementById('address-form');

        this.debounceTimer = null;
        this.debounceDelay = 300;

        this.init();
    }

    async init() {
        try {
            // Import Google Maps Places library
            const { Place, AutocompleteSessionToken, AutocompleteSuggestion } =
            await google.maps.importLibrary("places");

                this.Place = Place;
                this.AutocompleteSessionToken = AutocompleteSessionToken;
                this.AutocompleteSuggestion = AutocompleteSuggestion;

                // Create a new session token
                this.sessionToken = new AutocompleteSessionToken();

                if (this.addressInput) {
                this.setupEventListeners();
                }

            //   console.log('Address autocomplete initialized successfully');
        } catch (error) {
            console.error('Error initializing Google Places API:', error);
        }
    }

    setupEventListeners() {
        // Input event listener met debouncing
        this.addressInput.addEventListener('input', (e) => {
            const query = e.target.value.trim();

            if (query.length < 2) {
                this.hideSuggestions();
                return;
            }

            // Clear previous timer
            if (this.debounceTimer) {
                clearTimeout(this.debounceTimer);
            }

            // Set new timer
            this.debounceTimer = setTimeout(() => {
                this.fetchSuggestions(query);
            }, this.debounceDelay);
        });

        // Keyboard navigation
        this.addressInput.addEventListener('keydown', (e) => {
            if (!this.suggestionsDropdown.classList.contains('show')) return;

            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    this.navigateSuggestions(1);
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    this.navigateSuggestions(-1);
                    break;
                case 'Enter':
                    e.preventDefault();
                    if (this.selectedIndex >= 0) {
                        this.selectSuggestion(this.currentSuggestions[this.selectedIndex]);
                    }
                    break;
                case 'Escape':
                    this.hideSuggestions();
                    break;
            }
        });

        // Click outside to close dropdown
        document.addEventListener('click', (e) => {
            if (!this.addressInput.contains(e.target) && !this.suggestionsDropdown.contains(e.target)) {
                this.hideSuggestions();
            }
        });
    }

    async fetchSuggestions(query) {
        let $language = '';
        let $region = '';

        if ( site_language == 'nl-be' ) {
            $language = 'nl';
            $region = 'BE';
        } else if ( site_language == 'be-fr' ) {
            $language = 'fr';
            $region = 'BE';
        } else if ( site_language == 'de' ) {
            $language = 'de';
            $region = 'DE';
        } else if ( site_language == 'us' ) {
            $language = 'en';
            $region = 'en-US';
        } else if ( site_language == 'en' ) {
            $language = 'en';
            $region = 'en-GB';
        } else {
            $language = site_language;
            $region = site_language;
        }
        try {
            const request = {
                input: query,
                language: $language,
                region: $region,
                sessionToken: this.sessionToken,
                includedRegionCodes: [$region]
                // Remove includedPrimaryTypes to get more results, we'll filter in displaySuggestions
            };

            const { suggestions } = await this.AutocompleteSuggestion.fetchAutocompleteSuggestions(request);

            this.currentSuggestions = suggestions;
            this.displaySuggestions(suggestions);

        } catch (error) {
            console.error('Error fetching suggestions:', error);
            this.hideSuggestions();
        }
    }

    getRegionBounds(region) {
        // Define bounding boxes for different regions to restrict search area
        // Using the correct format for Google Places API locationRestriction
        const bounds = {
            'NL': {
                low: { lat: 50.5, lng: 3.2 },
                high: { lat: 53.7, lng: 7.3 }
            },
            'BE': {
                low: { lat: 49.5, lng: 2.5 },
                high: { lat: 51.7, lng: 6.5 }
            },
            'DE': {
                low: { lat: 47.3, lng: 5.9 },
                high: { lat: 55.1, lng: 15.0 }
            }
        };

        return bounds[region] || bounds['NL']; // Default to Netherlands
    }

    isCompleteAddress(placePrediction) {
        const mainText = placePrediction.text.text;
        const types = placePrediction.types || [];

        console.log('Checking address:', mainText, 'Types:', types);

        // Must be a street address type
        if (!types.includes('street_address')) {
            console.log('Rejected: Not a street_address type');
            return false;
        }

        // Must contain a house number at the beginning of the address
        // Look for patterns like "123 Street Name" or "Street Name 123"
        const hasHouseNumberAtStart = /^\d+\s/.test(mainText);
        const hasHouseNumberInMiddle = /\s\d+[\s,]/.test(mainText);
        const hasHouseNumberAtEnd = /\s\d+$/.test(mainText);

        const hasHouseNumber = hasHouseNumberAtStart || hasHouseNumberInMiddle || hasHouseNumberAtEnd;

        // Must contain street indicators
        const streetIndicators = /\b(straat|street|str|laan|weg|plein|kade|gracht|singel|boulevard|avenue|ave|road|rd|lane|ln|drive|dr|court|ct|place|pl|park|hof|steeg|passage|galerij|promenade|rue|place)\b/i;
        const hasStreetIndicator = streetIndicators.test(mainText);

        // Must NOT be just a street name without house number
        // Reject patterns like "Damrak, Amsterdam" (no house number)
        const isJustStreetName = !hasHouseNumber && hasStreetIndicator;

        const result = hasHouseNumber && hasStreetIndicator && !isJustStreetName;

        console.log('Address check result:', result, {
            hasHouseNumber,
            hasStreetIndicator,
            isJustStreetName,
            mainText
        });

        return result;
    }

    validateCompleteAddress(place) {
        if (!place.addressComponents) {
            return false;
        }

        let hasStreetNumber = false;
        let hasRoute = false;
        let hasPostalCode = false;
        let hasLocality = false;

        place.addressComponents.forEach(component => {
            const types = component.types;

            if (types.includes('street_number')) {
                hasStreetNumber = true;
            }
            if (types.includes('route')) {
                hasRoute = true;
            }
            if (types.includes('postal_code')) {
                hasPostalCode = true;
            }
            if (types.includes('locality') || types.includes('administrative_area_level_2')) {
                hasLocality = true;
            }
        });

        // All components must be present for a complete address
        return hasStreetNumber && hasRoute && hasPostalCode && hasLocality;
    }

    displaySuggestions(suggestions) {
        this.suggestionsDropdown.innerHTML = '';
        this.selectedIndex = -1;

        console.log('Raw suggestions received:', suggestions);

        // Smart filtering for address suggestions
        const filteredSuggestions = suggestions.filter(suggestion => {
            const placePrediction = suggestion.placePrediction;
            const text = placePrediction.text.text;
            const types = placePrediction.types || [];

            console.log(`Checking: "${text}"`, { types });

            // Priority 1: Street addresses with house numbers
            if (types.includes('street_address')) {
                const hasHouseNumber = /\b\d+\b/.test(text);
                if (hasHouseNumber) {
                    console.log(`✓ Accepted street address: ${text}`);
                    return true;
                }
            }

            // Priority 2: Establishments with clear addresses (like shops with house numbers)
            if (types.includes('establishment') && types.includes('point_of_interest')) {
                const hasHouseNumber = /\b\d+\b/.test(text);
                // Only accept if it has a house number and doesn't look like a generic business
                if (hasHouseNumber && !text.includes('B.V.') && !text.includes('Ltd')) {
                    console.log(`✓ Accepted establishment with address: ${text}`);
                    return true;
                }
            }

            console.log(`✗ Rejected: ${text}`);
            return false;
        });

        console.log('Filtered suggestions:', filteredSuggestions.length);

        if (filteredSuggestions.length === 0) {
            document.querySelectorAll('.address_unknown_wrap').forEach(element => {
                element.style.display = 'block';
            });
            this.hideSuggestions();
            return;
        }

        document.querySelectorAll('.address_unknown_wrap').forEach(element => {
            element.style.display = 'none';
        });

        filteredSuggestions.forEach((suggestion, index) => {
            const placePrediction = suggestion.placePrediction;
            const listItem = document.createElement('li');
            listItem.className = 'suggestion-item';
            listItem.dataset.index = index;

            const mainText = placePrediction.text.text;
            const secondaryText = placePrediction.structuredFormat?.secondaryText?.text || '';

            listItem.innerHTML = `
                <div class="suggestion-main">${this.highlightMatch(mainText, this.addressInput.value)}</div>
                ${secondaryText ? `<div class="suggestion-secondary">${secondaryText}</div>` : ''}
            `;

            listItem.addEventListener('click', () => {
                this.selectSuggestion(suggestion);
            });

            this.suggestionsDropdown.appendChild(listItem);
        });

        this.showSuggestions();
    }

    highlightMatch(text, query) {
        if (!query) return text;

        const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
        return text.replace(regex, '<strong>$1</strong>');
    }

    navigateSuggestions(direction) {
        const items = this.suggestionsDropdown.querySelectorAll('.suggestion-item');

        // Remove current highlight
        if (this.selectedIndex >= 0) {
            items[this.selectedIndex].classList.remove('highlighted');
        }

        // Update selected index
        this.selectedIndex += direction;

        if (this.selectedIndex < 0) {
            this.selectedIndex = items.length - 1;
        } else if (this.selectedIndex >= items.length) {
            this.selectedIndex = 0;
        }

        // Add new highlight
        items[this.selectedIndex].classList.add('highlighted');
        items[this.selectedIndex].scrollIntoView({ block: 'nearest' });
    }

    async selectSuggestion(suggestion) {
        try {
            const placePrediction = suggestion.placePrediction;

            // Hide suggestions
            this.hideSuggestions();

            // Get detailed place information
            const place = placePrediction.toPlace();

            await place.fetchFields({
                fields: [
                'id',
                'formattedAddress',
                'addressComponents',
                'location',
                'displayName'
                ]
            });

            // Validate that this is a complete address before filling fields
            if (!this.validateCompleteAddress(place)) {
                console.warn('Selected address is not complete, skipping:', place.formattedAddress);
                return;
            }

            // Fill hidden fields and display information
            this.fillAddressFields(place);

            // Update input field
            this.addressInput.value = place.formattedAddress || placePrediction.text.text;

            // Create new session token for next search
            this.sessionToken = new this.AutocompleteSessionToken();

        } catch (error) {
            console.error('Error selecting suggestion:', error);
        }
    }

    fillAddressFields(place) {
        // Reset all fields
        this.clearAddressFields();

        // Basic information
        // document.getElementById('place-id').value = place.id || '';
        // document.getElementById('formatted-address').value = place.formattedAddress || '';

        // Coordinates
        // if (place.location) {
        //   document.getElementById('latitude').value = place.location.lat();
        //   document.getElementById('longitude').value = place.location.lng();
        // }

        let street = '';
        let housenumber = '';
        let city = '';
        let zipcode = '';

        // Parse address components
        if (place.addressComponents) {
            place.addressComponents.forEach(component => {
                const types = component.types;
                const longName = component.longText;

                // document.querySelectorAll('[data-name="housenumber"]').forEach(e => {
                //     e.closest('.form-group').style.display = 'block';
                // });
                // document.querySelectorAll('[data-name="streetname"]').forEach(e => {
                //     e.closest('.form-group').style.display = 'block';
                // });


                if (types.includes('street_number')) {
                    housenumber = longName;
                    document.querySelectorAll('[data-name="housenumber"]').forEach(e => {
                        e.value = longName;
                    });
                } else if (types.includes('route')) {
                    street = longName;
                    document.querySelectorAll('[data-name="streetname"]').forEach(e => {
                        e.value = longName;
                    });
                } else if (types.includes('locality')) {
                    city = longName;
                    document.querySelectorAll('[data-name="city"]').forEach(e => {
                        e.value = longName;
                    });
                } else if (types.includes('postal_code')) {
                    zipcode = longName;
                    document.querySelectorAll('[data-name="zipcode"]').forEach(e => {
                        e.value = longName;
                    });
                }
            });


            var addressFieldWrap = document.getElementById('addressfinder_field');
            if (addressFieldWrap && addressFieldWrap._x_dataStack && addressFieldWrap._x_dataStack[0]) {
                var dataStack = addressFieldWrap._x_dataStack[0];

                if ( dataStack.google_maps == 1 ) {
                    dataStack.address_selected = true;
                    let link = 'https://maps.googleapis.com/maps/api/streetview?location=';
                    const linkOptions = '&fov=80&pitch=0&return_error_codes=true&size=524x393';
                    const apiKey = 'AIzaSyAhLZ0U3QUHDfniUvCDwApXANh1DuiRXrs';
                    let address = street + ',' + housenumber + ',' + city;
                    let fullLink = link + address + linkOptions + '&key=' + apiKey;

                    document.querySelector('.streetview').src = fullLink;

                    if ( dataStack.loading_screen == 1 ) {
                        document.querySelector('.loading-screen').style.display = 'flex';
                        setTimeout(function() {
                            document.querySelector('.loading-screen').style.display = 'none';
                            document.querySelector('.streetview-block').style.display = 'block';
                            document.querySelector('.streetview').style.display = 'block';
                        }, 3000);
                    } else {
                        document.querySelector('.streetview-block').style.display = 'block';
                        document.querySelector('.streetview').style.display = 'block';
                    }
                }
            }

        } else {
            console.log('No address components found');
        }

        // Display the selected address information
        // this.displaySelectedAddress(place);
    }

    clearAddressFields() {
        // const hiddenFields = [
        //   'place-id', 'formatted-address', 'street-number', 'route',
        //   'locality', 'state', 'postal-code', 'country', 'latitude', 'longitude'
        // ];

        // hiddenFields.forEach(fieldId => {
        //   const field = document.getElementById(fieldId);
        //   if (field) field.value = '';
        // });

        // this.selectedAddressDiv.style.display = 'none';
        document.querySelectorAll('[data-name="housenumber"]').forEach(element => {
            element.value = '';
        });
        document.querySelectorAll('[data-name="streetname"]').forEach(element => {
            element.value = '';
        });
        document.querySelectorAll('[data-name="city"]').forEach(element => {
            element.value = '';
        });
        document.querySelectorAll('[data-name="zipcode"]').forEach(element => {
            element.value = '';
        });
    }

    showSuggestions() {
        this.suggestionsDropdown.classList.add('show');
    }

    hideSuggestions() {
        this.suggestionsDropdown.classList.remove('show');
        this.selectedIndex = -1;
    }
}

// Initialize the autocomplete when the page loads
document.addEventListener('DOMContentLoaded', () => {
  new AddressAutocomplete();
});
