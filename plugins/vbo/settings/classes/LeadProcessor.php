<?php

namespace Vbo\Settings\Classes;

use Session;
use October\Rain\Network\Http;
use Input;
use Log;
use Mail;
use Vbo\Settings\Classes\EntryFunctions;
use Vbo\Settings\Classes\BagEp;
use Detection\MobileDetect;
use Vbo\SiteConfiguration\Models\SiteConfigSettings;
use Vbo\Settings\Classes\ErrorMail;

class LeadProcessor
{

    public function sendData($formData, $form)
    {
        $destination = config('vbo.lead_processor_url');
        $responseHashCode = 0;
        $postbackKey = '';
        $uid = '';

        if ( Session::has('landingpage_first') ) {
            $referrer = Session::get('landingpage_first');
        } else {
            $referrer = Session::get('landingpage');
        }

        $referrer = str_replace('/?', '?', $referrer);

        if ( str_contains($referrer, 'www.tab13-2.nl') && array_key_exists('taboola_hash', $formData) ) {
            $referrer = $referrer . $formData['taboola_hash'];
        }

        Session::forget('referrer');
        Session::put('referrer', $referrer);


        $data = $this->collectData($formData, $form);

        try {
            $response = Http::post($destination, function ($http) use ($referrer, $data) {
                $http->requestOptions = [ CURLOPT_FOLLOWLOCATION => false, ];
                $http->header('Referer', $referrer);
                $http->header('Origin', "https://{$_SERVER['HTTP_HOST']}");
                $http->data($data);
            });

            if ( array_key_exists('location', $response->headers) ) {
                $responseLocation = $response->headers['location'];

                // Parse "x" hash from "location" header.
                if ($responseLocation) {
                    if (preg_match('/(?:\?|&)x=([^\/]+)/i', $responseLocation, $matches)) {
                        $responseHashCode = $matches[1];
                    }
                }

                if (Session::has('responseHashCode')) {
                    Session::forget('responseHashCode');
                }
                Session::put('responseHashCode', $responseHashCode);
            } else {
                $this->sendErrorMessage($data);
            }

        } catch ( \Exception $e ) {
            $this->sendErrorMessage($data);
        }

        $postbackKey = $form->postback_key;
        if ( $form->postback_key ) {
            $postbackData = Input::get($postbackKey);

            if (Session::has('postbackData')) {
                Session::forget('postbackData');
            }
            Session::put('postbackData', $postbackData);
        }

        return true;
    }

    private function collectData($formData, $form)
    {
        $entryFunctions = new EntryFunctions;
        $sendMortgage = new SendMortgage;
        $getEnergyLabel = '';
        $test_domain = config('vbo.test_domain');
        $sitelang = SiteConfigSettings::get('tos_privacy_language');

        if ( $sitelang == 'nl' && array_key_exists('postcode', $formData) && array_key_exists('huisnummer', $formData) ) {
            $getBagData = new BagEp ('l7798acea511cf4c119b3412a5a34f1417', 'OTIyN0JEQUZFNEY4ODMyN0FDRTU1ODI0RUE4OTJGMDFDQjZDMTc2NzM4QTVDOEI2OTI5RUJDRTQ1RDQ2QzJDOTJEMTQ1RTQ4RUFCQURBMEFGQUNDOTM2ODJDNTE3QzQz');
            $getEnergyLabel = $getBagData->getEnergyLabel($formData['postcode'], $formData['huisnummer']);
        }

        // E-mail typo correction
        $mailaddress = $formData['emailadres'];
        $validmail = '';

        $reasons = $this->getReasons($formData);
        $dob = $this->getDob($formData);

        $country = $entryFunctions->getCountryName($sitelang);

        $countryCode = '';
        if ( array_key_exists('countryCode', $formData) ) {
            if ( $formData['countryCode'] != '' ) {
                $countryCode = $formData['countryCode'];
            } else {
                if ( $sitelang != '') {
                    $countryCode = $sitelang;
                }
            }
        } else {
            if ( $sitelang != '') {
                $countryCode = $sitelang;
            }
        }
        // Log::info($countryCode);

        $opmerkingen = '';
        $cc_opmerkingen = '';

        if ( isset($formData['additionConnect']) ) {
            $housenumber = $formData['huisnummer'] . $formData['additionConnect'];
        } else {
            $housenumber = isset( $formData['huisnummer'] ) ? $formData['huisnummer'] : '';
        }

        if ( isset( $formData['lopende_kredieten'] ) ) {
            $lopende_kredieten = $formData['lopende_kredieten'] == "Ja" ? 1 : 0;
        } elseif ( isset( $formData['lopende_leningen'] ) ) {
            $lopende_kredieten = $formData['lopende_leningen'] == "Ja" ? 1 : 0;
        } else {
            $lopende_kredieten = 0;
        }

        $detect = new MobileDetect();
        $device = 'Desktop';
        if ($detect->isMobile()) {
            $device = 'Mobiel';
        }
        if ($detect->isTablet()) {
            $device = 'Tablet';
        }

        $data = [
            'geslacht'                      => isset( $formData['aanhef'] ) ? $formData['aanhef'] : '',
            'initialen'                     => isset( $formData['voorletters'] ) ? $formData['voorletters'] : '',
            'voornaam'                      => isset( $formData['voornaam'] ) ? $formData['voornaam'] : '',
            'naam'                          => isset( $formData['achternaam'] ) ? $formData['achternaam'] : '',
            'straat'                        => isset( $formData['straatnaam'] ) ? $formData['straatnaam'] : '',
            'huisnr'                        => $housenumber,
            'postcode'                      => isset( $formData['postcode'] ) ? $formData['postcode'] : '',
            'woonplaats'                    => isset( $formData['woonplaats'] ) ? $formData['woonplaats'] : '',
            'tel'                           => isset( $formData['telefoonnummer'] ) ? $formData['telefoonnummer'] : '',
            'email'                         => isset( $mailaddress ) ? $mailaddress : '',
            'newsletter'                    => isset( $formData['nieuwsbrief'] ) ? $formData['nieuwsbrief'] : 0,
            'origin'                        => isset( $formData['ua_code'] ) ? $formData['ua_code'] : '',
            'time'                          => date("h:i:sa"),
            'io_uip'                        => $_SERVER['REMOTE_ADDR'],
            'io_uid'                        => $this->getUid(),
            'lead_type'                     => isset( $formData['lead_type'] ) ? $formData['lead_type'] : '',
            'reden'                         => isset( $formData['reden'] ) ? $formData['reden'] : '',
            'nbc_reden2'                    => isset( $reasons['2'] ) ? $reasons['2'] : '',
            'nbc_reden3'                    => isset( $reasons['3'] ) ? $reasons['3'] : '',
            'nbc_reden4'                    => isset( $reasons['4'] ) ? $reasons['4'] : '',
            'nbc_reden5'                    => isset( $reasons['5'] ) ? $reasons['5'] : '',
            'nbc_reden6'                    => isset( $reasons['6'] ) ? $reasons['6'] : '',
            'nbc_reden7'                    => isset( $reasons['7'] ) ? $reasons['7'] : '',
            'nbc_reden8'                    => isset( $reasons['8'] ) ? $reasons['8'] : '',
            'first_house'                   => isset( $formData['eerste_koopwoning'] ) ? $formData['eerste_koopwoning'] : 'Nee',
            'lopende_leningen'              => $lopende_kredieten,
            'partner'                       => isset( $formData['partner'] ) ? $formData['partner'] : '',
            'Geboortedatum_dag'             => isset( $dob['d'] ) ? $dob['d'] : '',
            'Geboortedatum_maand'           => isset( $dob['m'] ) ? $dob['m'] : '',
            'Geboortedatum_jaar'            => isset( $dob['y'] ) ? $dob['y'] : '',
            'country'                       => $country,
            'tel_country_code'              => $countryCode,
            'device'                        => $device,
            'user_agent'                   => isset( $_SERVER['HTTP_USER_AGENT'] ) ? $_SERVER['HTTP_USER_AGENT'] : '',
            'nbc_woonsituatie'              => isset( $formData['nbc_woonsituatie'] ) ? $formData['nbc_woonsituatie'] : '',
            'nbc_verwachte_aankoopprijs'    => isset( $waardetekopen ) ? $this->remove_euro($waardetekopen) : '',
            'nbc_huidige_waarde'            => isset( $formData['nbc_huidige_waarde'] ) ? $this->remove_euro($formData['nbc_huidige_waarde']) : '',
            'nbc_huidige_geldverstrekker'   => isset( $formData['nbc_huidige_geldverstrekker'] ) ? $formData['nbc_huidige_geldverstrekker'] : '',
            'nbc_nhg'                       => isset( $formData['nbc_nhg'] ) ? $formData['nbc_nhg'] : '',
            'nbc_hypotheek_start'           => isset( $formData['nbc_hypotheek_start'] ) ? $this->remove_euro($formData['nbc_hypotheek_start']) : '',
            'nbc_hypotheek_now'             => isset( $formData['nbc_hypotheek_now'] ) ? $this->remove_euro($formData['nbc_hypotheek_now']) : '',
            'nbc_hypotheek_rente'           => isset( $formData['nbc_hypotheek_rente'] ) ? $formData['nbc_hypotheek_rente'] : '',
            'nbc_ingang_hypotheek'          => isset( $formData['nbc_ingang_hypotheek'] ) ? $formData['nbc_ingang_hypotheek'] : '',
            'nbc_type_hypotheek'            => isset( $formData['nbc_type_hypotheek'] ) ? $formData['nbc_type_hypotheek'] : '',
        ];

        if ( $form->nat_form ) {
            $mortgageData = $sendMortgage->sendData($formData);
            $maandinkomen = 0;
            $maandinkomen_partner = 0;
            $broninkomen = isset( $formData['broninkomen'] ) ? $formData['broninkomen'] : '';
            $broninkomen_partner = isset( $formData['broninkomen_partner'] ) ? $formData['broninkomen_partner'] : '';
            $nbc_maandinkomen_partner = 0;
            $nbc_bruto_toekomstig_pensioeninkomen_partner = 0;
            $nbc_bruto_pensioeninkomen_partner = 0;


            if ( isset($formData['geboortedatum']) ) {
                $age = $entryFunctions->getAge($formData['geboortedatum']);
            }
            if ( isset($formData['geboortedatum_partner']) ) {
                $age_partner = $entryFunctions->getAge($formData['geboortedatum_partner']);
            }

            if ( isset( $mortgageData['monthly_income1'] ) ) {
                $maandinkomen = $mortgageData['monthly_income1'];
            }
            if ( isset( $mortgageData['monthly_income2'] ) ) {
                $maandinkomen_partner = $mortgageData['monthly_income2'];
            }
            if ( isset($age) ) {
                if ( $age <= 56  ) {
                    $nbc_maandinkomen = $maandinkomen;
                    $nbc_bruto_toekomstig_pensioeninkomen = 0;
                    $nbc_bruto_pensioeninkomen = 0;
                }
                if ( $age > 56 && $age < 67  ) {
                    $nbc_maandinkomen = 0;
                    $nbc_bruto_toekomstig_pensioeninkomen = $maandinkomen;
                    $nbc_bruto_pensioeninkomen = 0;
                }
                if ( $age >= 67  ) {
                    $broninkomen = 'Pensioen';
                    $nbc_maandinkomen = 0;
                    $nbc_bruto_toekomstig_pensioeninkomen = 0;
                    $nbc_bruto_pensioeninkomen = $maandinkomen;
                }
            }
            if ( isset($age_partner) ) {
                if ( $age_partner <= 56  ) {
                    $nbc_maandinkomen_partner = $maandinkomen_partner;
                    $nbc_bruto_toekomstig_pensioeninkomen_partner = 0;
                    $nbc_bruto_pensioeninkomen_partner = 0;
                }
                if ( $age_partner > 56 && $age_partner < 67  ) {
                    $nbc_maandinkomen_partner = 0;
                    $nbc_bruto_toekomstig_pensioeninkomen_partner = $maandinkomen_partner;
                    $nbc_bruto_pensioeninkomen_partner = 0;
                }
                if ( $age_partner >= 67  ) {
                    $broninkomen_partner = 'Pensioen';
                    $nbc_maandinkomen_partner = 0;
                    $nbc_bruto_toekomstig_pensioeninkomen_partner = 0;
                    $nbc_bruto_pensioeninkomen_partner = $maandinkomen_partner;
                }
            }

            $data['aankoopsom'] = $mortgageData['desired_mortgage'];
            $data['marktwaarde_woning'] = $mortgageData['property_value'];
            $data['broninkomen'] = $broninkomen;
            $data['broninkomen_partner'] = $broninkomen_partner;
            $data['maandinkomen'] = $nbc_maandinkomen;
            $data['nbc_bruto_toekomstig_pensioeninkomen'] = $nbc_bruto_toekomstig_pensioeninkomen;
            $data['nbc_bruto_pensioeninkomen'] = $nbc_bruto_pensioeninkomen;
            $data['maandinkomen2'] = $nbc_maandinkomen_partner;
            $data['nbc_bruto_toekomstig_pensioeninkomen_partner'] = $nbc_bruto_toekomstig_pensioeninkomen_partner;
            $data['nbc_bruto_pensioeninkomen_partner'] = $nbc_bruto_pensioeninkomen_partner;
            $data['nbc_geboortedatum_partner'] = $mortgageData['date_of_birth2'];
            $data['nbc_hypotheek_voor_2013'] = $mortgageData['mortgage_before_2013'];
            $data['nbc_openstaande_kredietsaldo'] = $mortgageData['loans'];
            $data['nbc_bruto_hypotheeklast'] = isset( $formData['hypotheeklast'] ) ? $formData['hypotheeklast'] : '';
            $data['nbc_woonlast'] = isset( $formData['woonlast'] ) ? $formData['woonlast'] : '';
        } else {
            $data['broninkomen'] = isset( $formData['broninkomen'] ) ? $formData['broninkomen'] : '';
            $data['broninkomen_partner'] = $broninkomen_partner = isset( $formData['broninkomen_partner'] ) ? $formData['broninkomen_partner'] : '';
            $data['marktwaarde_woning'] = isset( $formData['marktwaarde_woning'] ) ? $formData['marktwaarde_woning'] : '';
            $data['aankoopsom'] = isset( $formData['aankoopsom'] ) ? $formData['aankoopsom'] : '';
            $data['maandinkomen'] = isset( $formData['maandinkomen'] ) ? $formData['maandinkomen'] : '';
        }

        $defaultData = ['aanhef','voorletters','voornaam','achternaam','straatnaam','huisnummer','postcode','woonplaats','telefoonnummer','emailadres','ua_code','_session_key','_token','additionConnect','addition','naam','landingpage','lead_type','geboortedatum','geboortedatum_age','dobDay','dobMonth', 'dobYear','validmail', 'utm_source', 'countryCode', 'tel_country_code', 'bruto_toekomstig_pensioeninkomen_partner', 'bruto_pensioeninkomen_partner', 'bruto_maandinkomen_partner', 'geboortedatum_partner', 'geboortedatum_partner_age', 'bruto_toekomstig_pensioeninkomen', 'bruto_pensioeninkomen', 'bruto_maandinkomen', 'openstaand_kredietsaldo', 'hypotheeklast', 'hypotheek_voor_2013', 'waarde_woning', 'gewenst_hypotheekbedrag', 'oversluiten', 'aanpassen', 'aankoopfase', 'eerste_koopwoning', 'waarde_te_kopen_woning', 'woonlast', 'oversluiten_reden2', 'oversluiten_reden3', 'oversluiten_reden4', 'oversluiten_reden5', 'aanpassen_reden2', 'aanpassen_reden3', 'aanpassen_reden4', 'aanpassen_reden5', 'overig_reden2', 'overig_reden3', 'overig_reden4', 'overig_reden5', 'aankoopfase_reden2', 'aankoopfase_reden3', 'aankoopfase_reden4', 'aankoopfase_reden5', 'one-time-code', 'nieuwsbrief', 'valid_from', 'phonetype', 'taboola_hash', 'adres'];
        $dataKeyArray = array_keys($data);

        $utmSource = isset($formData['utm_source']) ? $formData['utm_source'] : '';
        if ( $form->send_utm ) {
            $opmerkingen .= 'utm_source: ' . $utmSource . ' | ';
        }

        // Possible reason search needles
        $reasonNeedles = ["_reden2", "_reden3", "_reden4", "_reden5", "_reden6", "_reden7", "_reden8"];

        // Loop through all form fields
        foreach ($formData as $key => $value) {

            // Check if field is not in default field list
            if ( !in_array($key, $defaultData) && !in_array($key, $dataKeyArray) ) {

                // Loop throught reason needles
                foreach ($reasonNeedles as $needle) {
                    if (strpos($key, $needle) !== false) {
                        $key = str_replace($needle, "", $key);
                    }
                }

                // Add fields to opmerkingen column
                // If field value is array, implode values

                if ( !str_starts_with($key, 'my_name_') ) {
                    if ( is_array($value) ) {
                        $opmerkingen .= $key . ': ' . implode(", ", $value) . ' | ';
                    } else {
                        $opmerkingen .= $key . ': ' . $value . ' | ';
                    }
                }
            }
        }

        // If dutch form and energylabel is set, add energylabel to opmerkingen column
        if ( $sitelang == 'nl' ) {
            if ( $getEnergyLabel && array_key_exists('label', $getEnergyLabel) ) {
                $opmerkingen .= 'Energielabel: ' . $getEnergyLabel['label'] . ' | ';
            } else {
                $opmerkingen .= 'Energielabel: onbekend | ';
            }
        }

        // If form has OTP, add one-time-code to opmerkingen column
        if ( isset($formData['one-time-code']) ) {
            $opmerkingen .= 'mid: ' . $formData['one-time-code'] . ' | ';

            $data['otp'] = 1;
        }

        // Add cookie consent settings to cc_opmerkingen column
        $cookieConsentName = 'CookieConsent';
        if ( isset($_COOKIE[$cookieConsentName]) ) {
            $cc_opmerkingen .= $_COOKIE[$cookieConsentName];
        }

        // Add _fbp cookie to data
        $fbpCookieName = '_fbp';
        if ( isset($_COOKIE[$fbpCookieName]) ) {
            $data['raw_cookie'] = urlencode($_COOKIE[$fbpCookieName]);
        }

        $data['opmerkingen'] = $opmerkingen;
        $data['_cc_opmerkingen'] = $cc_opmerkingen;

        // Debugging
        if ( $test_domain ) {
            $this->onTesting($formData);
            $this->onTesting($data);
        }

        if (Session::has('lead_data')) {
            Session::forget('lead_data');
        }
        Session::put('lead_data', $data);

        return $data;
    }

    private function remove_euro($value)
    {
        if ( strpos($value, '€') !== false ) {
            $value = preg_replace("/[^0-9\.]/", "", $value);
        }
        return $value;
    }

    private function getUid()
    {
        if ( Session::has('original_uid') ) {
            return Session::get('original_uid');
        }

        $visitor = new Visitor;
        return $visitor->getVisitor();
    }

    private function getReasons($formData)
    {
        $reasons = [];
        $reason2Needle = "_reden2";
        $reason3Needle = "_reden3";
        $reason4Needle = "_reden4";
        $reason5Needle = "_reden5";
        $reason6Needle = "_reden6";
        $reason7Needle = "_reden7";
        $reason8Needle = "_reden8";

        foreach($formData as $key => $value) {
            if (strpos($key, $reason2Needle) !== false) {
                $reasons['2'] = is_array($value) ? implode(", ", $value) : $value;
            }
            if (strpos($key, $reason3Needle) !== false) {
                $reasons['3'] = is_array($value) ? implode(", ", $value) : $value;
            }
            if (strpos($key, $reason4Needle) !== false) {
                $reasons['4'] = is_array($value) ? implode(", ", $value) : $value;
            }
            if (strpos($key, $reason5Needle) !== false) {
                $reasons['5'] = is_array($value) ? implode(", ", $value) : $value;
            }
            if (strpos($key, $reason6Needle) !== false) {
                $reasons['6'] = is_array($value) ? implode(", ", $value) : $value;
            }
            if (strpos($key, $reason7Needle) !== false) {
                $reasons['7'] = is_array($value) ? implode(", ", $value) : $value;
            }
            if (strpos($key, $reason8Needle) !== false) {
                $reasons['8'] = is_array($value) ? implode(", ", $value) : $value;
            }
        }

        return $reasons;
    }

    private function getDob($formData)
    {
        $dob = [];
        $entryFunctions = new EntryFunctions;

        if ( isset($formData['dobDay']) || isset($formData['dobMonth']) || isset($formData['dobYear'])  ) {
            $dob['d'] = $formData['dobDay'];
            $dob['m'] = $formData['dobMonth'];
            $dob['y'] = $formData['dobYear'];
        } else {
            if ( isset($formData['geboortedatum']) && $formData['geboortedatum'] != '--' ) {
                $dob['d'] = $entryFunctions->getDay($formData['geboortedatum']);
                $dob['m'] = $entryFunctions->getMonth($formData['geboortedatum']);
                $dob['y'] = $entryFunctions->getYear($formData['geboortedatum']);
            }
        }

        return $dob;
    }

    public function saveNaw($data)
    {
        $nawData = [
            'straat' => isset( $data['straatnaam'] ) ? $data['straatnaam'] : '',
            'huisnr' => isset( $data['huisnummer'] ) ? $data['huisnummer'] : '',
            'postcode' => isset( $data['postcode'] ) ? $data['postcode'] : '',
            'woonplaats' => isset( $data['woonplaats'] ) ? $data['woonplaats'] : '',
            'tel' => isset( $data['telefoonnummer'] ) ? $data['telefoonnummer'] : '',
            'email' => isset( $data['emailadres'] ) ? $data['emailadres'] : '',
        ];
        Session::put('NAW', $nawData);
    }

    public function savePixels($form)
    {
        $pixels = [];
        if ( $form->converse_pixels ) {
            $tracking = new Tracking;
            $showPixels = $tracking->showTrackingPixels();

            if ( $showPixels ) {
                foreach ( $form->converse_pixels as $item) {
                    $pixelCheck = $tracking->showPixel($item);

                    if ( $pixelCheck ) {
                        $pixels[] = $item;
                    }
                }
            }
        }
        if (Session::has('pixels')) {
            Session::forget('pixels');
        }
        Session::put('pixels', $pixels);
    }

    private function sendErrorMessage($data)
    {
        $errorMailWebsite = Session::get('landingpage');
        if ( Session::has('landingpage_first') ) {
            $errorMailWebsite = Session::get('landingpage_first');
        }

        $errorMailData = [
            'data' => $data,
            'website' => $errorMailWebsite,
        ];

        $errorMailSql = new ErrorMail;
        $sqlQuery = $errorMailSql->buildLeadInsertStatement($data);

        // Create temporary SQL file
        $tempSqlFile = null;
        if (!empty($sqlQuery)) {
            $tempSqlFile = tempnam(sys_get_temp_dir(), 'lead_insert_') . '.sql';
            file_put_contents($tempSqlFile, $sqlQuery);
        }

        Mail::send('vbo.settings::mail.missedlead', $errorMailData, function($message) use ($tempSqlFile) {
            $message->to('<EMAIL>', 'Thomas van Doorn');
            $message->cc('<EMAIL>', 'Kevin Klonne');
            $message->subject('Error in leadprocessor');

            // Attach SQL file if it was created
            if ($tempSqlFile && file_exists($tempSqlFile)) {
                $message->attach($tempSqlFile, [
                    'as' => 'lead_insert_' . date('Y-m-d_H-i-s') . '.sql',
                    'mime' => 'text/plain'
                ]);
            }
        });

        // Clean up temporary file
        if ($tempSqlFile && file_exists($tempSqlFile)) {
            unlink($tempSqlFile);
        }
    }

    private function onTesting($testData)
    {
        Log::info(json_encode($testData));
    }
}
