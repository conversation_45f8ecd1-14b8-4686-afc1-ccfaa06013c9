<?php

namespace Vbo\Settings\Classes;

class ErrorMail
{

    /**
    * Build a raw SQL INSERT statement for the `leads` table from a mapped data array.
    *
    * Behavior:
    * - Keys in `$mappedLead` are used as column names (backticked in the SQL).
    * - String values are single-quoted with single quotes escaped by doubling (').
    * - Columns in `$numericColumns` are treated as numeric; non-numeric values fall back to 0.
    * - Columns `updated_at` and `inserted` are set to NOW().
    * - `null` values become SQL NULL (unquoted).
    * - Returns an empty string when `$mappedLead` is empty.
    *
    * @param array $mappedLead Associative array: column => value
    * @return string The SQL statement, e.g. "INSERT INTO `leads` (`col1`, ...) VALUES (...);"
    */

    public function buildLeadInsertStatement(array $mappedLead): string
    {
        if (empty($mappedLead)) {
            return '';
        }

        $numericColumns = [
            'address_valid_flags',
            'locked_by_user_id',
            'manual_approved',
            'trap_renovatie',
            'energie',
            'double_score',
            'is_double',
            'double_checked',
            'haalbaar_nieuw',
            'haalbaar_gh_nieuw',
            'address_valid',
            'is_valid_phone',
            'cc_is_used',
            'unsubscribed',
            'has_lopende_leningen',
        ];

        $columns = array_keys($mappedLead);
        $keys = '`' . implode('`, `', $columns) . '`';

        $values = [];
        foreach ($columns as $column) {
            $value = $mappedLead[$column];

            // Timestamps use NOW()
            if ($column === 'updated_at' || $column === 'inserted') {
                $values[] = 'NOW()';
                continue;
            }

            // NULL handling
            if ($value === null) {
                $values[] = 'NULL';
                continue;
            }

            // Numeric columns (fallback to 0 when not numeric)
            if (in_array($column, $numericColumns, true)) {
                $values[] = is_numeric($value) ? (string)$value : '0';
                continue;
            }

            // Everything else: treat as string; escape single quotes by doubling
            $string = (string)$value;
            $escaped = str_replace("'", "''", $string);
            $values[] = "'{$escaped}'";
        }

        $valuesSql = implode(', ', $values);

        return "INSERT INTO `leads` ({$keys}) VALUES ({$valuesSql});";
    }
}
