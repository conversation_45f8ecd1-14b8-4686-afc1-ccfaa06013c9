<?php namespace Vbo\Settings\Components;

use Cms\Classes\ComponentBase;
use Http;
use Log;
use October\Rain\Exception\AjaxException;
use October\Rain\Exception\ValidationException;
use Renatio\FormBuilder\Classes\FormValidator;
use Renatio\FormBuilder\Models\Form;
use Renatio\FormBuilder\Models\FormLog;
use Renatio\FormBuilder\Traits\SupportLocationFields;
use Renatio\SpamProtection\Components\SpamProtection;
use System\Models\File;
use Throwable;
use Mail;
use Session;
use Vbo\Settings\Classes\ThankYouTemplates;
use Vbo\SiteConfiguration\Models\SiteConfigSettings;

class VboRenderForm extends ComponentBase
{
    use SupportLocationFields;

    public $form;

    public $markup;

    public $message;

    public $messageType = 'danger';

    public $hasFiles = false;

    public $formSubmitted = false;

    public $responseHashCode = 0;

    public $postbackData;

    public $sitelang;

    public function componentDetails()
    {
        return [
            'name' => 'Kies je Leverancier Formulier',
            'description' => 'renatio.formbuilder::lang.render_form.description',
            'snippetAjax' => true,
        ];
    }

    public function defineProperties()
    {
        return [
            'formCode' => [
                'title' => 'renatio.formbuilder::lang.form.title',
                'description' => 'renatio.formbuilder::lang.form.description',
                'type' => 'dropdown',
                'validation' => ['required' => true],
                'default' => 'default-form',
            ],
        ];
    }

    public function onRun()
    {
        try {
            $this->form = $this->getForm();

            event('formBuilder.overrideForm', [&$this->form]);

            $this->handleFileUploads();

            $this->addComponent(SpamProtection::class, 'spamProtection');
        } catch (Throwable $throwable) {
            $this->page['formCode'] = $this->getFormCode();
        }
    }

    public function onRender()
    {
        $this->markup = $this->getFormMarkup();

        $this->addJs('assets/js/form.js?v=1');

        $this->sitelang = $this->getSiteLang();

        $this->form->language = $this->sitelang;

        if ( request()->get('bedankt') ) {
            $this->formSubmitted = true;
            if ( Session::has('postbackData') ) {
                $this->postbackData = Session::get('postbackData');
            }
            $this->responseHashCode = Session::get('responseHashCode');
        }
    }

    public function onSubmit()
    {
        $validator = (new FormValidator($this->form))->make();

        if ($validator->fails()) {
            throw new ValidationException($validator);
        }

        try {
            $ty_templates = new ThankYouTemplates;
            $template = '';
            if ( $this->form->ty_message_source != 'current' ) {
                $template = $ty_templates->getTemplate($this->form->ty_message_source, $this->form->ty_message_code);
            }
            if ( $template != '' ) {
                Session::put('ty_message', $template);
            }
            event('formBuilder.formSubmitted', [$this->form]);
        } catch (Throwable $throwable) {
            $this->message = app()->environment('production') ? $this->form->error_message : $throwable->getMessage();

            trace_log($throwable);

            if ( Session::has('landingpage_first') ) {
                $errorMailWebsite = Session::get('landingpage_first');
            } else {
                $errorMailWebsite = Session::get('landingpage');
            }

            $errorMailData = [
                'error' => $throwable,
                'website' => $errorMailWebsite,
            ];

            Mail::send('vbo.settings::mail.errormessage', $errorMailData, function($message) {
                $message->to('<EMAIL>', 'Thomas van Doorn');
                $message->cc('<EMAIL>', 'Kevin Klonne');
                $message->subject('Error melding');
            });

            throw new AjaxException([".form-alert-{$this->form->id}" => $this->renderPartial('@message')]);
        }

        return $this->response();
    }

    public function getFormCodeOptions()
    {
        return Form::lists('name', 'code');
    }

    protected function getForm()
    {
        return Form::query()
            ->with([
                'fields' => fn ($query) => $query->isVisible()->with('field_type'),
            ])
            ->where('code', $this->getFormCode())
            ->firstOrFail();
    }

    protected function getFormCode()
    {
        return $this->getController()->vars['formCode'] ?? $this->property('formCode');
    }

    protected function response()
    {
        if ($this->form->redirect_to) {
            return redirect()->to($this->form->redirect_to);
        }

        $this->messageType = 'success';
        $this->message = $this->form->success_message;

        return [".form-alert-{$this->form->id}" => $this->renderPartial('@message')];
    }

    protected function getFormMarkup()
    {
        return $this->form?->fields->reduce(function ($template, $field) {
            $pattern = "/{{\sform_field\('$field->name'\)\s}}/i";

            return preg_replace($pattern, $field->html, $template);
        }, $this->form->getMarkup());
    }

    protected function handleFileUploads()
    {
        $this->hasFiles = ! $this->form->uploadFields()->isEmpty();

        foreach ($this->form->uploadFields() as $field) {
            FormLog::extend(function ($model) use ($field) {
                $attachType = $field->has_multiple_files ? 'attachMany' : 'attachOne';

                $model->$attachType[$field->name] = [
                    File::class,
                ];
            });
        }
    }

    private function getExternalData($site)
    {
        $settings = Http::get($site);

        if ( $settings->failed() ) {
            return false;
        }

        $result = json_decode($settings, true);

        if ( !$result ) {
            return false;
        }

        return $result;
    }

    private function getSiteLang()
    {
        $lang = '';
        if ( SiteConfigSettings::get('tos_privacy_language') ) {
            $lang = SiteConfigSettings::get('tos_privacy_language');
        }

        return $lang;
    }

    // public function onSetFormDataSession()
    // {
    //     $sessionName = $this->param('sessionName');
    //     $data = $this->param('data');
    //     $valid = false;
    //     if ( $sessionName != '' && $data != '' ) {
    //         Session::put($sessionName, $data);
    //         $valid = true;
    //     }
    //     return [
    //         'valid' => $valid
    //     ];
    // }
}
